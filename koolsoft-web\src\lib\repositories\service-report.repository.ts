import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Service Report Repository
 *
 * This repository handles database operations for the Service Report entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class ServiceReportRepository extends PrismaRepository<
  Prisma.service_reportsGetPayload<{}>,
  string,
  Prisma.service_reportsCreateInput,
  Prisma.service_reportsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('service_reports');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): ServiceReportRepository {
    return new ServiceReportRepository(tx);
  }

  /**
   * Find service reports with related data
   * @param id Service report ID
   * @returns Promise resolving to the service report with relations or null
   */
  async findWithRelations(id: string): Promise<any> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
            phone: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        details: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  }

  /**
   * Find service reports by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service reports
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { reportDate: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
          },
        },
        details: true,
      },
    });
  }

  /**
   * Find service reports by status
   * @param status Service report status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service reports
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { status },
      skip,
      take,
      orderBy: { reportDate: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Find service reports by executive ID
   * @param executiveId Executive ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service reports
   */
  async findByExecutiveId(executiveId: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { executiveId },
      skip,
      take,
      orderBy: { reportDate: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
        details: true,
      },
    });
  }

  /**
   * Find service reports with filtering and pagination
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Order by criteria
   * @returns Promise resolving to filtered service reports
   */
  async findWithFilter(
    filter: any = {},
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<any[]> {
    try {
      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);
      
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { reportDate: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
          details: {
            select: {
              id: true,
              machineType: true,
              serialNumber: true,
              problem: true,
              solution: true,
              partReplaced: true,
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('ServiceReportRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count service reports with filter
   * @param filter Filter criteria
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any = {}): Promise<number> {
    try {
      this.validateFilter(filter);
      return this.model.count({
        where: filter,
      });
    } catch (error) {
      console.error('ServiceReportRepository.countWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Get service report statistics
   * @returns Promise resolving to service report statistics
   */
  async getStatistics(): Promise<any> {
    const [
      totalReports,
      openReports,
      completedReports,
      pendingReports,
      recentReports,
    ] = await Promise.all([
      this.model.count(),
      this.model.count({ where: { status: 'OPEN' } }),
      this.model.count({ where: { status: 'COMPLETED' } }),
      this.model.count({ where: { status: 'PENDING' } }),
      this.model.count({
        where: {
          reportDate: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    return {
      totalReports,
      openReports,
      completedReports,
      pendingReports,
      recentReports,
      completionRate: totalReports > 0 ? (completedReports / totalReports) * 100 : 0,
    };
  }

  /**
   * Create service report with details
   * @param serviceReportData Service report data
   * @param details Service details data
   * @returns Promise resolving to the created service report
   */
  async createWithDetails(serviceReportData: any, details: any[] = []): Promise<any> {
    return this.prisma.$transaction(async (tx) => {
      // Create the service report
      const serviceReport = await tx.service_reports.create({
        data: serviceReportData,
      });

      // Create service details if provided
      if (details && details.length > 0) {
        await tx.service_details.createMany({
          data: details.map((detail: any) => ({
            ...detail,
            serviceReportId: serviceReport.id,
          })),
        });
      }

      // Return the service report with related data
      return tx.service_reports.findUnique({
        where: { id: serviceReport.id },
        include: {
          customer: true,
          executive: true,
          details: true,
        },
      });
    });
  }

  /**
   * Update service report status
   * @param id Service report ID
   * @param status New status
   * @param completionDate Completion date (optional)
   * @returns Promise resolving to the updated service report
   */
  async updateStatus(id: string, status: string, completionDate?: Date): Promise<any> {
    const updateData: any = { status };
    if (status === 'COMPLETED' && completionDate) {
      updateData.completionDate = completionDate;
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        customer: true,
        executive: true,
        details: true,
      },
    });
  }

  /**
   * Validate filter object to prevent injection attacks
   * @param filter Filter object to validate
   */
  private validateFilter(filter: any): void {
    const allowedFields = [
      'id',
      'customerId',
      'executiveId',
      'status',
      'reportDate',
      'visitDate',
      'completionDate',
      'natureOfService',
      'complaintType',
      'originalId',
    ];

    for (const key in filter) {
      if (!allowedFields.includes(key)) {
        throw new Error(`Invalid filter field: ${key}`);
      }
    }
  }
}

// Repository factory function
let serviceReportRepository: ServiceReportRepository | null = null;

export function getServiceReportRepository(): ServiceReportRepository {
  if (!serviceReportRepository) {
    serviceReportRepository = new ServiceReportRepository();
  }
  return serviceReportRepository;
}
